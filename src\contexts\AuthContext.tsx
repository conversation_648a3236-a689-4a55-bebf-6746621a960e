

import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import type { User } from '@supabase/supabase-js';

import { Session } from '@supabase/supabase-js';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { getUserEntitlements, invalidateUserEntitlements } from '@/lib/entitlements/cache';
import { hasEntitlement, hasContextualEntitlement } from '@/lib/entitlements';
import { validateUserSubscription, invalidateOnSubscriptionEvent } from '@/lib/api/subscriptions/validation';
import { isFeatureEnabled, SUBSCRIPTION_FEATURE_FLAGS } from '@/lib/feature-flags';
import type { SubscriptionStatus } from '@/lib/api/subscriptions/types';

type AuthContextType = {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, username: string) => Promise<void>;
  signOut: () => Promise<void>;

  // Club membership info
  clubRoles: Record<string, string>; // clubId -> role
  fetchClubRoles: () => Promise<void>;
  isAdmin: (clubId: string) => boolean;
  isMember: (clubId: string) => boolean;

  // Entitlements
  entitlements: string[];
  entitlementsLoading: boolean;
  refreshEntitlements: () => Promise<void>;
  hasEntitlement: (entitlement: string) => boolean;
  hasContextualEntitlement: (prefix: string, contextId: string) => boolean;

  // Subscription state (Phase 4B.1.1)
  subscriptionStatus: SubscriptionStatus | null;
  subscriptionLoading: boolean;
  refreshSubscriptionStatus: () => Promise<void>;
  hasValidSubscription: () => boolean;
  getSubscriptionTier: () => 'MEMBER' | 'PRIVILEGED' | 'PRIVILEGED_PLUS';
  hasRequiredTier: (tier: 'PRIVILEGED' | 'PRIVILEGED_PLUS') => boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<Session['user'] | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [clubRoles, setClubRoles] = useState<Record<string, string>>({});
  const [entitlements, setEntitlements] = useState<string[]>([]);
  const [entitlementsLoading, setEntitlementsLoading] = useState(true);

  // Subscription state (Phase 4B.1.1)
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(false);

  const navigate = useNavigate();

  const fetchClubRoles = async () => {
    if (!user) return;
    try {
      const { data, error } = await supabase
        .from('club_members')
        .select('club_id, role')
        .eq('user_id', user.id)
        .not('role', 'eq', 'pending');

      if (error) {
        toast.error('Failed to load club membership data');
        return;
      }

      const rolesMap: Record<string, string> = {};
      data?.forEach((row: any) => {
        rolesMap[row.club_id] = row.role;
      });
      setClubRoles(rolesMap);
    } catch (err) {
      toast.error('Failed to load club membership data');
    }
  };

  const isAdmin = (clubId: string) => {
    return clubRoles[clubId] === 'admin';
  };

  const isMember = (clubId: string) => {
    return clubRoles.hasOwnProperty(clubId) && clubRoles[clubId] !== 'pending';
  };

  // Subscription functions with Phase 4A integration
  const refreshSubscriptionStatus = async () => {
    if (!user) {
      setSubscriptionStatus(null);
      setSubscriptionLoading(false);
      return;
    }

    try {
      setSubscriptionLoading(true);
      console.log(`[AuthContext] Refreshing subscription status for user ${user.id}`);

      // Check if subscription integration is enabled via feature flag
      const subscriptionIntegrationEnabled = await isFeatureEnabled(
        SUBSCRIPTION_FEATURE_FLAGS.SUBSCRIPTION_VALIDATION
      );

      if (!subscriptionIntegrationEnabled.enabled) {
        console.log('[AuthContext] Subscription integration disabled via feature flag');
        setSubscriptionStatus(null);
        return;
      }

      // Use optimized subscription validation from Phase 4A.2.1
      const validationResult = await validateUserSubscription(user.id, {
        useCache: true,
        failSecure: true,
        timeout: 5000 // 5 second timeout for AuthContext
      });

      if (validationResult.success) {
        setSubscriptionStatus(validationResult.status);
        console.log(`[AuthContext] Subscription status updated:`, {
          hasActiveSubscription: validationResult.status.hasActiveSubscription,
          currentTier: validationResult.status.currentTier,
          validationSource: validationResult.status.validationSource
        });
      } else {
        console.warn('[AuthContext] Subscription validation failed, using null status');
        setSubscriptionStatus(null);
      }

    } catch (error) {
      console.error('[AuthContext] Error refreshing subscription status:', error);
      // Graceful degradation - don't show error to user for subscription data
      setSubscriptionStatus(null);
    } finally {
      setSubscriptionLoading(false);
    }
  };

  // Subscription helper functions
  const hasValidSubscription = (): boolean => {
    return subscriptionStatus?.hasActiveSubscription && subscriptionStatus?.isValid || false;
  };

  const getSubscriptionTier = (): 'MEMBER' | 'PRIVILEGED' | 'PRIVILEGED_PLUS' => {
    return subscriptionStatus?.currentTier || 'MEMBER';
  };

  const hasRequiredTier = (tier: 'PRIVILEGED' | 'PRIVILEGED_PLUS'): boolean => {
    const currentTier = getSubscriptionTier();

    if (tier === 'PRIVILEGED') {
      return currentTier === 'PRIVILEGED' || currentTier === 'PRIVILEGED_PLUS';
    }

    if (tier === 'PRIVILEGED_PLUS') {
      return currentTier === 'PRIVILEGED_PLUS';
    }

    return false;
  };

  // Entitlements functions with improved caching
  const refreshEntitlements = async () => {
    if (!user) {
      setEntitlements([]);
      setEntitlementsLoading(false);
      return;
    }

    try {
      setEntitlementsLoading(true);

      // Force refresh by passing true as the second argument
      const userEntitlements = await getUserEntitlements(user.id, true);
      setEntitlements(userEntitlements);


    } catch (error) {
      toast.error('Failed to load user permissions');
    } finally {
      setEntitlementsLoading(false);
    }
  };

  const checkEntitlement = (entitlement: string) => {
    return hasEntitlement(entitlements, entitlement);
  };

  const checkContextualEntitlement = (prefix: string, contextId: string) => {
    return hasContextualEntitlement(entitlements, prefix, contextId);
  };

  // Store the last known user ID to detect genuine sign-ins vs refreshes
  const [lastKnownUserId, setLastKnownUserId] = useState<string | null>(null);

  // Main auth session effect - only depends on navigate
  useEffect(() => {
    // Track if this is the initial session fetch
    let isInitialMount = true;

    const fetchSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setSession(session);

      if (session?.user) {
        setUser(session?.user);
        // Store the user ID when we first load the session
        setLastKnownUserId(session.user.id);
      }

      setLoading(false);
    };

    fetchSession();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        // Only update session if it's a meaningful change
        setSession(session);

        if (session?.user) {
          setUser(session?.user);

          // Only show welcome message and navigate on a genuine new sign-in
          // This happens when the user ID changes from null to a value
          const isGenuineSignIn = event === 'SIGNED_IN' &&
                                !isInitialMount &&
                                lastKnownUserId === null;

          if (isGenuineSignIn) {
            toast.success(`Welcome back!`);
            navigate('/book-club');
            // Update the last known user ID
            setLastKnownUserId(session.user.id);
          } else if (lastKnownUserId !== session.user.id) {
            // Update the last known user ID without navigation
            // This handles user changes without tab switching
            setLastKnownUserId(session.user.id);
          }
        } else {
          // User signed out
          setUser(null);
          setClubRoles({});
          setLastKnownUserId(null);
          // Clear subscription data on sign out
          setSubscriptionStatus(null);
          setSubscriptionLoading(false);
        }

        setLoading(false);
        // After the first auth state change, we're no longer in the initial mount
        isInitialMount = false;
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [navigate, lastKnownUserId]); // Added lastKnownUserId as a dependency

  // Separate effect for fetching club roles when user changes
  useEffect(() => {
    if (user?.id) {
      fetchClubRoles();
    }
  }, [user?.id]);

  // Effect for loading entitlements when user changes
  useEffect(() => {
    if (user?.id) {
      // Set loading state
      setEntitlementsLoading(true);

      // Use the cached entitlements if available
      getUserEntitlements(user.id)
        .then(userEntitlements => {
          setEntitlements(userEntitlements);
        })
        .catch(() => {
          setEntitlements([]);
        })
        .finally(() => {
          setEntitlementsLoading(false);
        });
    } else {
      setEntitlements([]);
      setEntitlementsLoading(false);
    }
  }, [user?.id]);

  // Effect for loading subscription status when user changes (Phase 4B.1.1)
  useEffect(() => {
    if (user?.id) {
      // Load subscription status asynchronously (don't block authentication)
      refreshSubscriptionStatus().catch(error => {
        console.error('[AuthContext] Failed to load subscription status on user change:', error);
        // Graceful degradation - subscription data is optional
      });
    } else {
      setSubscriptionStatus(null);
      setSubscriptionLoading(false);
    }
  }, [user?.id]);

  const signIn = async (email: string, password: string) => {
    setLoading(true);

    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast.error(error.message || "Failed to sign in");
        return;
      }

      toast.success("Successfully signed in!");

      // Navigate will be handled by the auth state change listener
    } catch (error: any) {
      toast.error(error.message || "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, username: string) => {
    setLoading(true);

    try {
      // Create auth user with username in metadata for database trigger
      const { error, data } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: username
          }
        }
      });

      if (error) {
        toast.error(error.message || "Failed to sign up");
        return;
      }

      if (data.user) {
        toast.success("Account created! Welcome to BookConnect!");
        navigate('/book-club');
      }
    } catch (error: any) {
      toast.error(error.message || "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    setLoading(true);
    try {
      // Invalidate caches if user exists
      if (user?.id) {
        invalidateUserEntitlements(user.id);

        // Invalidate subscription cache using Phase 4A.1 integration
        try {
          await invalidateOnSubscriptionEvent(user.id, 'subscription_expired');
        } catch (error) {
          console.warn('[AuthContext] Failed to invalidate subscription cache on sign out:', error);
          // Non-critical error - continue with sign out
        }
      }

      await supabase.auth.signOut();
      setEntitlements([]);
      setSubscriptionStatus(null);
      toast.success("You've been successfully signed out");
      navigate('/login');
    } catch (error) {
      toast.error("Failed to sign out");
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      session,
      loading,
      signIn,
      signUp,
      signOut,
      clubRoles,
      fetchClubRoles,
      isAdmin,
      isMember,
      entitlements,
      entitlementsLoading,
      refreshEntitlements,
      hasEntitlement: checkEntitlement,
      hasContextualEntitlement: checkContextualEntitlement,
      // Subscription state (Phase 4B.1.1)
      subscriptionStatus,
      subscriptionLoading,
      refreshSubscriptionStatus,
      hasValidSubscription,
      getSubscriptionTier,
      hasRequiredTier
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
